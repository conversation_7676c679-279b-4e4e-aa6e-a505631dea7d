# Vault secrets for development environment
resource "vault_generic_secret" "development_secrets" {
  for_each = {
    for key, combination in local.vault_service_combinations : key => combination
    if combination.environment == "development"
  }
  provider = vault.development
  path     = "secret/${each.value.environment}/${each.value.service}"

  data_json = jsonencode({
    db_user     = each.value.service
    db_password = uuid()
  })
}

# Vault secrets for staging environment
resource "vault_generic_secret" "staging_secrets" {
  for_each = {
    for key, combination in local.vault_service_combinations : key => combination
    if combination.environment == "staging"
  }
  provider = vault.staging
  path     = "secret/${each.value.environment}/${each.value.service}"

  data_json = jsonencode({
    db_user     = each.value.service
    db_password = uuid()
  })
}

# Vault secrets for production environment
resource "vault_generic_secret" "production_secrets" {
  for_each = {
    for key, combination in local.vault_service_combinations : key => combination
    if combination.environment == "production"
  }
  provider = vault.production
  path     = "secret/${each.value.environment}/${each.value.service}"

  data_json = jsonencode({
    db_user     = each.value.service
    db_password = uuid()
  })
}

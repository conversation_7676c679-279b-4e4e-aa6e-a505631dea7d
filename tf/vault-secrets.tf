# Vault secrets for development environment
resource "vault_generic_secret" "development_secrets" {
  for_each = {
    for service_name, service_config in local.services : service_name => service_config
    if service_config.vault_enabled
  }
  provider = vault.development
  path     = "secret/development/${each.key}"

  data_json = jsonencode({
    db_user     = each.key
    db_password = uuid()
  })
}

# Vault secrets for staging environment
resource "vault_generic_secret" "staging_secrets" {
  for_each = {
    for service_name, service_config in local.services : service_name => service_config
    if service_config.vault_enabled
  }
  provider = vault.staging
  path     = "secret/staging/${each.key}"

  data_json = jsonencode({
    db_user     = each.key
    db_password = uuid()
  })
}

# Vault secrets for production environment
resource "vault_generic_secret" "production_secrets" {
  for_each = {
    for service_name, service_config in local.services : service_name => service_config
    if service_config.vault_enabled
  }
  provider = vault.production
  path     = "secret/production/${each.key}"

  data_json = jsonencode({
    db_user     = each.key
    db_password = uuid()
  })
}

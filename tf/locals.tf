# Configuration for environments and services
locals {
  environments = {
    development = {
      vault_address = "http://localhost:8201"
      vault_token   = "f23612cf-824d-4206-9e94-e31a6dc8ee8d"
      network_name  = "vagrant_development"
      frontend_port = 4080
      vault_host    = "vault-development"
    }
    staging = {
      vault_address = "http://localhost:8202"
      vault_token   = "staging-vault-token-12345"
      network_name  = "vagrant_staging"
      frontend_port = 4082
      vault_host    = "vault-staging"
    }
    production = {
      vault_address = "http://localhost:8301"
      vault_token   = "083672fc-4471-4ec4-9b59-a285e463a973"
      network_name  = "vagrant_production"
      frontend_port = 4081
      vault_host    = "vault-production"
    }
  }

  services = {
    account = {
      image         = "form3tech-oss/platformtest-account"
      vault_enabled = true
    }
    gateway = {
      image         = "form3tech-oss/platformtest-gateway"
      vault_enabled = true
    }
    payment = {
      image         = "form3tech-oss/platformtest-payment"
      vault_enabled = true
    }
    frontend = {
      image            = "docker.io/nginx:latest"
      vault_enabled    = false
      production_image = "docker.io/nginx:1.22.0-alpine"
    }
  }

  # Generate environment-service combinations for vault-enabled services
  vault_service_combinations = {
    for combination in flatten([
      for env_name, env_config in local.environments : [
        for service_name, service_config in local.services : {
          key         = "${service_name}-${env_name}"
          environment = env_name
          service     = service_name
          env_config  = env_config
          service_config = service_config
        }
        if service_config.vault_enabled
      ]
    ]) : combination.key => combination
  }
}

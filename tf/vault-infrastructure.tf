# Vault audit configuration for each environment
resource "vault_audit" "audit_development" {
  provider = vault.development
  type     = "file"

  options = {
    file_path = "/vault/logs/audit"
  }
}

resource "vault_audit" "audit_staging" {
  provider = vault.staging
  type     = "file"

  options = {
    file_path = "/vault/logs/audit"
  }
}

resource "vault_audit" "audit_production" {
  provider = vault.production
  type     = "file"

  options = {
    file_path = "/vault/logs/audit"
  }
}

# Vault auth backend configuration for each environment
resource "vault_auth_backend" "userpass_development" {
  provider = vault.development
  type     = "userpass"
}

resource "vault_auth_backend" "userpass_staging" {
  provider = vault.staging
  type     = "userpass"
}

resource "vault_auth_backend" "userpass_production" {
  provider = vault.production
  type     = "userpass"
}

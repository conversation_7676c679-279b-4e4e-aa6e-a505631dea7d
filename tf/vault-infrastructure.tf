# Vault audit configuration for development environment
resource "vault_audit" "audit_development" {
  provider = vault.development
  type     = "file"

  options = {
    file_path = "/vault/logs/audit"
  }
}

# Vault audit configuration for staging environment
resource "vault_audit" "audit_staging" {
  provider = vault.staging
  type     = "file"

  options = {
    file_path = "/vault/logs/audit"
  }
}

# Vault audit configuration for production environment
resource "vault_audit" "audit_production" {
  provider = vault.production
  type     = "file"

  options = {
    file_path = "/vault/logs/audit"
  }
}

# Vault auth backend configuration for development environment
resource "vault_auth_backend" "userpass_development" {
  provider = vault.development
  type     = "userpass"
}

# Vault auth backend configuration for staging environment
resource "vault_auth_backend" "userpass_staging" {
  provider = vault.staging
  type     = "userpass"
}

# Vault auth backend configuration for production environment
resource "vault_auth_backend" "userpass_production" {
  provider = vault.production
  type     = "userpass"
}

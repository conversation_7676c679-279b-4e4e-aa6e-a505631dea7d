# Dynamic vault providers for each environment
provider "vault" {
  address = "http://localhost:8201"
  token   = "f23612cf-824d-4206-9e94-e31a6dc8ee8d"
}

provider "vault" {
  alias   = "development"
  address = local.environments.development.vault_address
  token   = local.environments.development.vault_token
}

provider "vault" {
  alias   = "staging"
  address = local.environments.staging.vault_address
  token   = local.environments.staging.vault_token
}

provider "vault" {
  alias   = "production"
  address = local.environments.production.vault_address
  token   = local.environments.production.vault_token
}

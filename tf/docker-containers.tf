# Docker containers for development environment vault services
resource "docker_container" "development_vault_services" {
  for_each = {
    for key, combination in local.vault_service_combinations : combination.service => combination
    if combination.environment == "development"
  }

  image = each.value.service_config.image
  name  = "${each.key}_development"

  env = [
    "VAULT_ADDR=http://${each.value.env_config.vault_host}:8200",
    "VAULT_USERNAME=${each.value.service}-${each.value.environment}",
    "VAULT_PASSWORD=123-${each.value.service}-${each.value.environment}",
    "ENVIRONMENT=${each.value.environment}"
  ]

  networks_advanced {
    name = each.value.env_config.network_name
  }

  lifecycle {
    ignore_changes = all
  }

  depends_on = [vault_generic_endpoint.development_users]
}

# Docker containers for staging environment vault services
resource "docker_container" "staging_vault_services" {
  for_each = {
    for key, combination in local.vault_service_combinations : combination.service => combination
    if combination.environment == "staging"
  }

  image = each.value.service_config.image
  name  = "${each.key}_staging"

  env = [
    "VAULT_ADDR=http://${each.value.env_config.vault_host}:8200",
    "VAULT_USERNAME=${each.value.service}-${each.value.environment}",
    "VAULT_PASSWORD=123-${each.value.service}-${each.value.environment}",
    "ENVIRONMENT=${each.value.environment}"
  ]

  networks_advanced {
    name = each.value.env_config.network_name
  }

  lifecycle {
    ignore_changes = all
  }

  depends_on = [vault_generic_endpoint.staging_users]
}

# Docker containers for production environment vault services
resource "docker_container" "production_vault_services" {
  for_each = {
    for key, combination in local.vault_service_combinations : combination.service => combination
    if combination.environment == "production"
  }

  image = each.value.service_config.image
  name  = "${each.key}_production"

  env = [
    "VAULT_ADDR=http://${each.value.env_config.vault_host}:8200",
    "VAULT_USERNAME=${each.value.service}-${each.value.environment}",
    "VAULT_PASSWORD=123-${each.value.service}-${each.value.environment}",
    "ENVIRONMENT=${each.value.environment}"
  ]

  networks_advanced {
    name = each.value.env_config.network_name
  }

  lifecycle {
    ignore_changes = all
  }

  depends_on = [vault_generic_endpoint.production_users]
}

# Docker containers for frontend services (no vault integration)
resource "docker_container" "frontend_services" {
  for_each = local.environments

  image = each.key == "production" ? local.services.frontend.production_image : local.services.frontend.image
  name  = "frontend_${each.key}"

  ports {
    internal = 80
    external = each.value.frontend_port
  }

  networks_advanced {
    name = each.value.network_name
  }

  lifecycle {
    ignore_changes = all
  }
}

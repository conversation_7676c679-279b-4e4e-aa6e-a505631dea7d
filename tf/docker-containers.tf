# Docker containers for development environment vault services
resource "docker_container" "development_vault_services" {
  for_each = {
    for service_name, service_config in local.services : service_name => service_config
    if service_config.vault_enabled
  }

  image = each.value.image
  name  = "${each.key}_development"

  env = [
    "VAULT_ADDR=http://${local.environments.development.vault_host}:8200",
    "VAULT_USERNAME=${each.key}-development",
    "VAULT_PASSWORD=123-${each.key}-development",
    "ENVIRONMENT=development"
  ]

  networks_advanced {
    name = local.environments.development.network_name
  }

  lifecycle {
    ignore_changes = all
  }

  depends_on = [vault_generic_endpoint.development_users]
}

# Docker containers for staging environment vault services
resource "docker_container" "staging_vault_services" {
  for_each = {
    for service_name, service_config in local.services : service_name => service_config
    if service_config.vault_enabled
  }

  image = each.value.image
  name  = "${each.key}_staging"

  env = [
    "VAULT_ADDR=http://${local.environments.staging.vault_host}:8200",
    "VAULT_USERNAME=${each.key}-staging",
    "VAULT_PASSWORD=123-${each.key}-staging",
    "ENVIRONMENT=staging"
  ]

  networks_advanced {
    name = local.environments.staging.network_name
  }

  lifecycle {
    ignore_changes = all
  }

  depends_on = [vault_generic_endpoint.staging_users]
}

# Docker containers for production environment vault services
resource "docker_container" "production_vault_services" {
  for_each = {
    for service_name, service_config in local.services : service_name => service_config
    if service_config.vault_enabled
  }

  image = each.value.image
  name  = "${each.key}_production"

  env = [
    "VAULT_ADDR=http://${local.environments.production.vault_host}:8200",
    "VAULT_USERNAME=${each.key}-production",
    "VAULT_PASSWORD=123-${each.key}-production",
    "ENVIRONMENT=production"
  ]

  networks_advanced {
    name = local.environments.production.network_name
  }

  lifecycle {
    ignore_changes = all
  }

  depends_on = [vault_generic_endpoint.production_users]
}

# Docker containers for frontend services (no vault integration)
resource "docker_container" "frontend_services" {
  for_each = local.environments

  image = each.key == "production" ? local.services.frontend.production_image : local.services.frontend.image
  name  = "frontend_${each.key}"

  ports {
    internal = 80
    external = each.value.frontend_port
  }

  networks_advanced {
    name = each.value.network_name
  }

  lifecycle {
    ignore_changes = all
  }
}

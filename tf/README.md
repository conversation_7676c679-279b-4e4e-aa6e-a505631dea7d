# Terraform Infrastructure Refactoring

This document outlines the refactoring of the Form3 platform Terraform code to improve maintainability and scalability.

## File Structure

The Terraform configuration has been split into logical, modular files:

```
tf/
├── main.tf                    # Terraform version and provider requirements
├── locals.tf                 # Environment and service configuration
├── providers.tf              # Vault provider configurations
├── vault-infrastructure.tf   # Vault audit and auth backend setup
├── vault-secrets.tf          # Database secrets for each service/environment
├── vault-policies.tf         # Access policies for each service/environment
├── vault-users.tf            # User authentication endpoints
├── docker-containers.tf      # Docker container deployments
└── README.md                 # This documentation
```

### Benefits of Modular Structure

- **Separation of Concerns**: Each file has a single responsibility
- **Easy Navigation**: Find specific resources quickly
- **Parallel Development**: Teams can work on different files simultaneously
- **Reduced Merge Conflicts**: Changes are isolated to specific files
- **Easier Code Reviews**: Reviewers can focus on specific areas
- **Maintainability**: Easier to understand and modify individual components

## Design Decisions

### 1. Data-Driven Configuration

**Problem**: The original code had massive duplication with each service/environment combination requiring ~50 lines of nearly identical code.

**Solution**: Implemented a data-driven approach using Terraform locals to define environments and services as structured data:

```hcl
locals {
  environments = {
    development = { vault_address = "...", vault_token = "...", ... }
    staging     = { vault_address = "...", vault_token = "...", ... }
    production  = { vault_address = "...", vault_token = "...", ... }
  }
  
  services = {
    account  = { image = "form3tech-oss/platformtest-account", vault_enabled = true }
    gateway  = { image = "form3tech-oss/platformtest-gateway", vault_enabled = true }
    payment  = { image = "form3tech-oss/platformtest-payment", vault_enabled = true }
    frontend = { image = "docker.io/nginx:latest", vault_enabled = false }
  }
}
```

**Benefits**:
- Adding a new service requires only one entry in the `services` map
- Adding a new environment requires only one entry in the `environments` map
- Configuration is centralized and type-safe
- Reduces code from ~460 lines to ~220 lines

### 2. Dynamic Resource Creation with for_each

**Problem**: Resources were manually duplicated for each service/environment combination.

**Solution**: Used Terraform's `for_each` meta-argument to dynamically create resources:

```hcl
resource "vault_generic_secret" "service_secrets" {
  for_each = local.service_passwords
  provider = vault[each.value.environment]
  path     = "secret/${each.value.environment}/${each.value.service}"
  # ...
}
```

**Benefits**:
- Eliminates code duplication
- Ensures consistency across all environments
- Automatic scaling when new services/environments are added

### 3. Separation of Vault-Enabled vs Non-Vault Services

**Problem**: Frontend service doesn't need Vault integration but was mixed with other services.

**Solution**: Created separate resource blocks for vault-enabled services and frontend services:

```hcl
# Vault-enabled services (account, gateway, payment)
resource "docker_container" "vault_services" { ... }

# Frontend services (no vault integration)
resource "docker_container" "frontend_services" { ... }
```

**Benefits**:
- Clear separation of concerns
- Easier to add services that don't need Vault
- Simplified dependency management

### 4. Dynamic Provider Configuration

**Problem**: Vault providers were hardcoded for each environment.

**Solution**: Created dynamic providers using the environments configuration:

```hcl
provider "vault" {
  alias   = "development"
  address = local.environments.development.vault_address
  token   = local.environments.development.vault_token
}
```

**Benefits**:
- Providers automatically created for new environments
- Centralized provider configuration
- Consistent naming convention

## Adding New Services/Environments

### Adding a New Service

1. Add entry to `locals.services`:
```hcl
new_service = {
  image         = "your-org/new-service"
  vault_enabled = true  # or false if no vault needed
}
```

2. Run `terraform plan` and `terraform apply` - all environments will automatically get the new service.

### Adding a New Environment

1. Add entry to `locals.environments` in `locals.tf`:
```hcl
testing = {
  vault_address = "http://localhost:8203"
  vault_token   = "testing-token"
  network_name  = "vagrant_testing"
  frontend_port = 4083
  vault_host    = "vault-testing"
}
```

2. Add corresponding vault service to `docker-compose.yml`:
```yaml
vault-testing:
  networks:
    - testing
  image: hashicorp/vault:1.19
  ports:
    - "8203:8200"
  environment:
    - VAULT_DEV_ROOT_TOKEN_ID=testing-token
```

3. Run `terraform plan` and `terraform apply` - all services will automatically be deployed to the new environment.

## File Organization Guidelines

When modifying the Terraform configuration, follow these guidelines:

### locals.tf
- Contains all environment and service configuration
- Modify this file to add new environments or services
- Keep configuration data-driven and avoid hardcoding values elsewhere

### providers.tf
- Contains all provider configurations
- Add new provider aliases here when adding environments
- Keep provider configurations centralized

### vault-*.tf files
- Each file handles a specific aspect of Vault configuration
- `vault-infrastructure.tf`: Basic Vault setup (audit, auth backends)
- `vault-secrets.tf`: Database credentials and secrets
- `vault-policies.tf`: Access control policies
- `vault-users.tf`: User authentication endpoints

### docker-containers.tf
- Contains all Docker container deployments
- Separated by service type (vault-enabled vs frontend)
- Environment-specific configurations are pulled from locals

### main.tf
- Contains only Terraform and provider version requirements
- Keep this file minimal and focused on configuration metadata

## CI/CD Pipeline Integration

### Recommended Pipeline Structure

```yaml
stages:
  - validate
  - plan
  - apply-dev
  - test-dev
  - apply-staging
  - test-staging
  - apply-prod

validate:
  script:
    - terraform fmt -check
    - terraform validate
    - terraform plan -out=plan.tfplan

plan:
  script:
    - terraform plan -out=plan.tfplan
  artifacts:
    paths:
      - plan.tfplan

apply-dev:
  script:
    - terraform apply plan.tfplan
  environment: development
  only:
    - develop

apply-staging:
  script:
    - terraform apply plan.tfplan
  environment: staging
  only:
    - main

apply-prod:
  script:
    - terraform apply plan.tfplan
  environment: production
  when: manual
  only:
    - main
```

### Environment-Specific Configurations

Use Terraform workspaces or separate state files for each environment:

```bash
# Option 1: Workspaces
terraform workspace new development
terraform workspace new staging
terraform workspace new production

# Option 2: Separate state files
terraform init -backend-config="key=terraform/development.tfstate"
terraform init -backend-config="key=terraform/staging.tfstate"
terraform init -backend-config="key=terraform/production.tfstate"
```

### Secret Management

- Store Vault tokens in CI/CD secret management (GitLab CI Variables, GitHub Secrets, etc.)
- Use different tokens per environment
- Rotate tokens regularly
- Consider using Vault's CI/CD auth methods instead of static tokens

## Production Considerations

### 1. State Management

**Current Issue**: State is stored locally, not suitable for team collaboration.

**Recommendations**:
- Use remote state backend (S3 + DynamoDB, Terraform Cloud, etc.)
- Enable state locking to prevent concurrent modifications
- Implement state backup and recovery procedures

```hcl
terraform {
  backend "s3" {
    bucket         = "your-terraform-state"
    key            = "form3-platform/terraform.tfstate"
    region         = "us-west-2"
    dynamodb_table = "terraform-locks"
    encrypt        = true
  }
}
```

### 2. Security Hardening

**Vault Security**:
- Use proper Vault authentication methods (AWS IAM, Kubernetes, etc.)
- Implement Vault policies with least privilege access
- Enable Vault audit logging
- Use Vault's secret rotation capabilities
- Store Vault unseal keys securely (HSM, cloud KMS)

**Network Security**:
- Implement proper network segmentation
- Use TLS for all communications
- Restrict access to Vault ports
- Implement proper firewall rules

### 3. Monitoring and Observability

**Infrastructure Monitoring**:
- Monitor Vault health and performance
- Set up alerts for container failures
- Monitor resource utilization
- Implement log aggregation

**Application Monitoring**:
- Health checks for all services
- Distributed tracing
- Metrics collection
- Error tracking

### 4. Disaster Recovery

**Backup Strategy**:
- Regular Vault data backups
- Terraform state backups
- Container image backups
- Configuration backups

**Recovery Procedures**:
- Document recovery procedures
- Test recovery regularly
- Implement automated recovery where possible
- Maintain recovery time objectives (RTO) and recovery point objectives (RPO)

### 5. Compliance and Governance

**Access Control**:
- Implement RBAC for Terraform operations
- Audit all infrastructure changes
- Require code reviews for infrastructure changes
- Implement approval workflows for production changes

**Compliance**:
- Ensure data encryption at rest and in transit
- Implement proper data retention policies
- Maintain audit trails
- Regular security assessments

### 6. Scalability Considerations

**Container Orchestration**:
- Consider migrating to Kubernetes for better scalability
- Implement auto-scaling policies
- Use load balancers for high availability
- Implement rolling deployments

**Database Scaling**:
- Implement database clustering
- Use read replicas for scaling reads
- Implement proper backup and recovery for databases
- Consider database sharding for large datasets

### 7. Cost Optimization

**Resource Management**:
- Implement resource tagging for cost tracking
- Use appropriate instance sizes
- Implement auto-shutdown for non-production environments
- Regular cost reviews and optimization

## Testing Strategy

### Infrastructure Testing

```bash
# Validate Terraform syntax
terraform fmt -check
terraform validate

# Security scanning
tfsec .
checkov -f main.tf

# Plan validation
terraform plan -out=plan.tfplan
terraform show -json plan.tfplan | jq
```

### Integration Testing

```bash
# Test service connectivity
curl http://localhost:4080  # development frontend
curl http://localhost:4082  # staging frontend
curl http://localhost:4081  # production frontend

# Test vault connectivity
vault status -address=http://localhost:8201  # development
vault status -address=http://localhost:8202  # staging
vault status -address=http://localhost:8301  # production
```

This refactored infrastructure provides a solid foundation for scaling the Form3 platform while maintaining security, reliability, and operational excellence.

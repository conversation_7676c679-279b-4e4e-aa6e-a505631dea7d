# Vault user endpoints for development environment
resource "vault_generic_endpoint" "development_users" {
  for_each = {
    for key, combination in local.vault_service_combinations : key => combination
    if combination.environment == "development"
  }
  provider             = vault.development
  depends_on           = [vault_auth_backend.userpass_development]
  path                 = "auth/userpass/users/${each.key}"
  ignore_absent_fields = true

  data_json = jsonencode({
    policies = [each.key]
    password = "123-${each.key}"
  })
}

# Vault user endpoints for staging environment
resource "vault_generic_endpoint" "staging_users" {
  for_each = {
    for key, combination in local.vault_service_combinations : key => combination
    if combination.environment == "staging"
  }
  provider             = vault.staging
  depends_on           = [vault_auth_backend.userpass_staging]
  path                 = "auth/userpass/users/${each.key}"
  ignore_absent_fields = true

  data_json = jsonencode({
    policies = [each.key]
    password = "123-${each.key}"
  })
}

# Vault user endpoints for production environment
resource "vault_generic_endpoint" "production_users" {
  for_each = {
    for key, combination in local.vault_service_combinations : key => combination
    if combination.environment == "production"
  }
  provider             = vault.production
  depends_on           = [vault_auth_backend.userpass_production]
  path                 = "auth/userpass/users/${each.key}"
  ignore_absent_fields = true

  data_json = jsonencode({
    policies = [each.key]
    password = "123-${each.key}"
  })
}

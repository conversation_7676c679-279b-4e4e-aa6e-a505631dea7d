# Vault user endpoints for development environment
resource "vault_generic_endpoint" "development_users" {
  for_each = {
    for service_name, service_config in local.services : service_name => service_config
    if service_config.vault_enabled
  }
  provider             = vault.development
  depends_on           = [vault_auth_backend.userpass_development]
  path                 = "auth/userpass/users/${each.key}-development"
  ignore_absent_fields = true

  data_json = jsonencode({
    policies = ["${each.key}-development"]
    password = "123-${each.key}-development"
  })
}

# Vault user endpoints for staging environment
resource "vault_generic_endpoint" "staging_users" {
  for_each = {
    for service_name, service_config in local.services : service_name => service_config
    if service_config.vault_enabled
  }
  provider             = vault.staging
  depends_on           = [vault_auth_backend.userpass_staging]
  path                 = "auth/userpass/users/${each.key}-staging"
  ignore_absent_fields = true

  data_json = jsonencode({
    policies = ["${each.key}-staging"]
    password = "123-${each.key}-staging"
  })
}

# Vault user endpoints for production environment
resource "vault_generic_endpoint" "production_users" {
  for_each = {
    for service_name, service_config in local.services : service_name => service_config
    if service_config.vault_enabled
  }
  provider             = vault.production
  depends_on           = [vault_auth_backend.userpass_production]
  path                 = "auth/userpass/users/${each.key}-production"
  ignore_absent_fields = true

  data_json = jsonencode({
    policies = ["${each.key}-production"]
    password = "123-${each.key}-production"
  })
}

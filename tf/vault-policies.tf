# Vault policies for development environment
resource "vault_policy" "development_policies" {
  for_each = {
    for service_name, service_config in local.services : service_name => service_config
    if service_config.vault_enabled
  }
  provider = vault.development
  name     = "${each.key}-development"

  policy = <<EOT
path "secret/data/development/${each.key}" {
    capabilities = ["list", "read"]
}
EOT
}

# Vault policies for staging environment
resource "vault_policy" "staging_policies" {
  for_each = {
    for service_name, service_config in local.services : service_name => service_config
    if service_config.vault_enabled
  }
  provider = vault.staging
  name     = "${each.key}-staging"

  policy = <<EOT
path "secret/data/staging/${each.key}" {
    capabilities = ["list", "read"]
}
EOT
}

# Vault policies for production environment
resource "vault_policy" "production_policies" {
  for_each = {
    for service_name, service_config in local.services : service_name => service_config
    if service_config.vault_enabled
  }
  provider = vault.production
  name     = "${each.key}-production"

  policy = <<EOT
path "secret/data/production/${each.key}" {
    capabilities = ["list", "read"]
}
EOT
}

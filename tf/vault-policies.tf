# Vault policies for development environment
resource "vault_policy" "development_policies" {
  for_each = {
    for key, combination in local.vault_service_combinations : key => combination
    if combination.environment == "development"
  }
  provider = vault.development
  name     = each.key

  policy = <<EOT
path "secret/data/${each.value.environment}/${each.value.service}" {
    capabilities = ["list", "read"]
}
EOT
}

# Vault policies for staging environment
resource "vault_policy" "staging_policies" {
  for_each = {
    for key, combination in local.vault_service_combinations : key => combination
    if combination.environment == "staging"
  }
  provider = vault.staging
  name     = each.key

  policy = <<EOT
path "secret/data/${each.value.environment}/${each.value.service}" {
    capabilities = ["list", "read"]
}
EOT
}

# Vault policies for production environment
resource "vault_policy" "production_policies" {
  for_each = {
    for key, combination in local.vault_service_combinations : key => combination
    if combination.environment == "production"
  }
  provider = vault.production
  name     = each.key

  policy = <<EOT
path "secret/data/${each.value.environment}/${each.value.service}" {
    capabilities = ["list", "read"]
}
EOT
}
